<?php
namespace App\Repositories\Library\OTP;
use Illuminate\Support\Facades\App;
use Ixudra\Curl\Facades\Curl;
use App\Eloquents\Users;

use App\Eloquents\UsersPurchaseOTP;
use App\Traits\ESLogTrait;

class esb_otp
{
    use ESLogTrait;

    public function __construct(){
		$this->url = env('ESB_URL');
        $this->cp_name = env('ESB_CP_NAME');
        $this->element1 = env('ESB_ELEMENT1');
        $this->client_id = env('ESB_CLIENT_ID');
        $this->email_client_id = env('ESB_EMAIL_CLIENT_ID');
        $this->proxy_host = env('PROXY_HOST');
		$this->proxy_port = env('PROXY_PORT');
        $this->proxy_protocol = env('PROXY_PROTOCOL');	
        $this->api_key = env('ESB_API_KEY');
        $this->secret = env('ESB_SECRET');	
        $this->channel = env('ESB_CHANNEL');
        $this->host = env('ESB_HOST');
	}
    
    public function send_otp_email($user,$msisdn) {
        $user_id = Users::find($user);
        $timestamp = gmdate('U');
        $sig = md5($this->api_key.$this->secret.$timestamp);
        date_default_timezone_set("Asia/Bangkok");
        $t=time();
        $milliseconds = (microtime(true) * 1000)%1000;
        $date = date("ymdHis",$t);
        $timestamp = $date.$milliseconds;//get timestamp
        $last_5_msisdn = substr((string) $msisdn, -5);//get 5 digit terakhir msisdn
        $transaction_id = 'SA-'.$timestamp.$last_5_msisdn.'0';
        $param = array(
            'transaction' => array(
                'transaction_id' => $transaction_id,
                'channel' => $this->channel 
            ),
            'service' => array(
                'service_id' => $msisdn,
            ),
            'otp' => array(
                'client_id' => $this->email_client_id,
                'element1' => $this->element1,
                'cp_name' => $this->cp_name,
                'email_info' => array(
                    'email' => $user_id->email,
                    'salutation' => 'Kak',
                    'first_name' => $user_id->name,
                    'last_name' => ','
                )
            ),
        );
        \Log::info(json_encode(['LOG' => 'Change SIM Card OTP Request','data' => $param]));

        $response = Curl::to($this->url.'/scrt/esb/v2/otp/email/request')
                    ->withData($param)
                    ->withResponseHeaders()
                    ->returnResponseObject()
			        ->withHeader('api_key: '.$this->api_key)
                    ->withHeader('x-signature: '.$sig)
                    ->withHeader('Host: '.$this->host)
                    ->asJsonRequest()
                    ->post();
        

        \Log::info(json_encode(['LOG'=>'Change SIM Card OTP Response','RESPONSE'=>$response]));


        return $response;
    }

    public function submit_otp_email($user,$msisdn,$token,$transaction_id){
        $user_id = Users::find($user);
        $timestamp = gmdate('U');
        $sig = md5($this->api_key.$this->secret.$timestamp);
        $param = array(
            'transaction' => array(
                'transaction_id' => $transaction_id,
                'channel' => $this->channel
            ),
            'service' => array(
                'service_id' => $msisdn
            ),
            'otp' => array(
                'client_id' => $this->email_client_id,
                'element1' => $this->element1,
                'cp_name' => $this->cp_name,
                'token' => $token,
                'email' => $user_id->email
            ),
        );
        \Log::info(json_encode(['log' => 'Change SIM Card OTP Request','data' => $param]));
        $response = Curl::to($this->url.'/scrt/esb/v2/otp/validation')
                    ->withData($param)
                    ->withResponseHeaders()
                    ->returnResponseObject()
                    ->withHeader('api_key: '.$this->api_key)
                    ->withHeader('x-signature: '.$sig)
                    ->withHeader('Host: '.$this->host)
                    ->asJsonRequest()
                    ->post();
        
        \Log::info(json_encode(['log' => 'Change SIM Card OTP Response','data' => $response]));
        return $response;
    }
    
}